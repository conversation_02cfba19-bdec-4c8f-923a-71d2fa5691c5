import asyncio
import csv
import json
from datetime import datetime
from playwright.async_api import async_playwright
import re
import time

class KlineChartScraper:
    def __init__(self, url="https://www.kline.co.jp/en/ir/finance/shipping.html"):
        self.url = url
        self.data_points = []
        
    async def scrape_chart_data(self, headless=True, viewport_width=1920, viewport_height=1080):
        """
        Scrape chart data by hovering over chart elements and extracting tooltip data
        """
        async with async_playwright() as p:
            # Launch browser
            browser = await p.chromium.launch(headless=headless)
            context = await browser.new_context(
                viewport={'width': viewport_width, 'height': viewport_height}
            )
            page = await context.new_page()
            
            try:
                print(f"Navigating to {self.url}")
                await page.goto(self.url, wait_until="networkidle")
                
                # Wait for the chart to load
                await page.wait_for_timeout(3000)
                
                # Find the chart container or SVG element
                chart_selectors = [
                    'svg',
                    '.highcharts-container',
                    '.chart-container',
                    '[class*="chart"]',
                    '[id*="chart"]'
                ]
                
                chart_element = None
                for selector in chart_selectors:
                    try:
                        chart_element = await page.wait_for_selector(selector, timeout=5000)
                        if chart_element:
                            print(f"Found chart element with selector: {selector}")
                            break
                    except:
                        continue
                
                if not chart_element:
                    print("Could not find chart element, trying alternative approach...")
                    # Take a screenshot for debugging
                    await page.screenshot(path="debug_page.png")
                    
                    # Try to find any SVG paths that might contain chart data
                    paths = await page.query_selector_all('path')
                    print(f"Found {len(paths)} path elements")
                    
                    for i, path in enumerate(paths):
                        d_attr = await path.get_attribute('d')
                        if d_attr and len(d_attr) > 100:  # Likely chart data
                            print(f"Found potential chart path {i}: {d_attr[:100]}...")
                            chart_element = path
                            break
                
                if chart_element:
                    await self.extract_data_from_chart(page, chart_element)
                else:
                    print("No chart element found, attempting to extract from page source...")
                    await self.extract_from_page_source(page)
                
            except Exception as e:
                print(f"Error during scraping: {e}")
                await page.screenshot(path="error_screenshot.png")
                
            finally:
                await browser.close()
    
    async def extract_data_from_chart(self, page, chart_element):
        """
        Extract data by hovering over chart points
        """
        print("Attempting to extract data by hovering over chart...")
        
        # Get chart boundaries
        chart_box = await chart_element.bounding_box()
        if not chart_box:
            print("Could not get chart bounding box")
            return
        
        print(f"Chart boundaries: {chart_box}")
        
        # Try to find tooltip selectors
        tooltip_selectors = [
            '.highcharts-tooltip',
            '.tooltip',
            '[class*="tooltip"]',
            '.chart-tooltip',
            '.hover-info'
        ]
        
        # Sample points across the chart width
        num_points = 50
        step = chart_box['width'] / num_points
        
        for i in range(num_points):
            x = chart_box['x'] + (i * step)
            y = chart_box['y'] + (chart_box['height'] / 2)  # Middle of chart
            
            try:
                # Hover over point
                await page.mouse.move(x, y)
                await page.wait_for_timeout(100)  # Wait for tooltip
                
                # Try to find tooltip
                tooltip_found = False
                for selector in tooltip_selectors:
                    tooltip = await page.query_selector(selector)
                    if tooltip:
                        tooltip_text = await tooltip.inner_text()
                        if tooltip_text.strip():
                            print(f"Point {i}: {tooltip_text.strip()}")
                            self.parse_tooltip_data(tooltip_text.strip(), i)
                            tooltip_found = True
                            break
                
                if not tooltip_found:
                    # Try to get data from page content changes
                    await self.check_for_data_changes(page, x, y, i)
                    
            except Exception as e:
                print(f"Error at point {i}: {e}")
                continue
    
    def parse_tooltip_data(self, tooltip_text, index):
        """
        Parse tooltip text to extract date and price information
        """
        # Common patterns for date and price in tooltips
        date_patterns = [
            r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})',  # YYYY-MM-DD or YYYY/MM/DD
            r'(\d{1,2}[-/]\d{1,2}[-/]\d{4})',  # MM-DD-YYYY or MM/DD/YYYY
            r'(\w{3}\s+\d{1,2},?\s+\d{4})',    # Mon DD, YYYY
        ]
        
        price_patterns = [
            r'(\d+\.?\d*)\s*(?:yen|¥|JPY)',     # Price in yen
            r'(\d+,?\d*\.?\d*)\s*(?:price|value|amount)',  # General price
            r'(\d+\.?\d*)',                      # Any number
        ]
        
        date_match = None
        price_match = None
        
        for pattern in date_patterns:
            match = re.search(pattern, tooltip_text, re.IGNORECASE)
            if match:
                date_match = match.group(1)
                break
        
        for pattern in price_patterns:
            match = re.search(pattern, tooltip_text, re.IGNORECASE)
            if match:
                price_match = match.group(1)
                break
        
        if date_match or price_match:
            data_point = {
                'index': index,
                'date': date_match,
                'price': price_match,
                'raw_tooltip': tooltip_text
            }
            self.data_points.append(data_point)
    
    async def check_for_data_changes(self, page, x, y, index):
        """
        Check for any data changes on the page when hovering
        """
        try:
            # Look for any elements that might show data
            data_selectors = [
                '[class*="data"]',
                '[class*="value"]',
                '[class*="price"]',
                '[class*="date"]',
                'span',
                'div'
            ]
            
            for selector in data_selectors:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    text = await element.inner_text()
                    if text and (re.search(r'\d{4}', text) or re.search(r'\d+\.?\d*', text)):
                        # Potential data found
                        self.parse_tooltip_data(text, index)
                        break
        except:
            pass
    
    async def extract_from_page_source(self, page):
        """
        Extract data from page source/JavaScript variables
        """
        print("Extracting data from page source...")
        
        # Get page content
        content = await page.content()
        
        # Look for JSON data in script tags
        json_patterns = [
            r'data\s*[:=]\s*(\[.*?\])',
            r'chartData\s*[:=]\s*(\[.*?\])',
            r'series\s*[:=]\s*(\[.*?\])',
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            for match in matches:
                try:
                    data = json.loads(match)
                    if isinstance(data, list) and len(data) > 0:
                        print(f"Found JSON data: {len(data)} items")
                        self.process_json_data(data)
                except:
                    continue
    
    def process_json_data(self, data):
        """
        Process JSON data extracted from page
        """
        for i, item in enumerate(data):
            if isinstance(item, dict):
                data_point = {
                    'index': i,
                    'date': item.get('date') or item.get('x'),
                    'price': item.get('price') or item.get('y') or item.get('value'),
                    'raw_data': item
                }
                self.data_points.append(data_point)
            elif isinstance(item, list) and len(item) >= 2:
                data_point = {
                    'index': i,
                    'date': item[0],
                    'price': item[1],
                    'raw_data': item
                }
                self.data_points.append(data_point)
    
    def save_to_csv(self, filename="kline_chart_data.csv"):
        """
        Save extracted data to CSV file
        """
        if not self.data_points:
            print("No data points to save")
            return
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['index', 'date', 'price', 'raw_tooltip']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for point in self.data_points:
                writer.writerow({
                    'index': point.get('index', ''),
                    'date': point.get('date', ''),
                    'price': point.get('price', ''),
                    'raw_tooltip': point.get('raw_tooltip', '')
                })
        
        print(f"Data saved to {filename}")
    
    def save_to_json(self, filename="kline_chart_data.json"):
        """
        Save extracted data to JSON file
        """
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.data_points, jsonfile, indent=2, ensure_ascii=False)
        
        print(f"Data saved to {filename}")
    
    def print_summary(self):
        """
        Print summary of extracted data
        """
        print(f"\n=== EXTRACTION SUMMARY ===")
        print(f"Total data points extracted: {len(self.data_points)}")
        
        if self.data_points:
            print(f"First few data points:")
            for i, point in enumerate(self.data_points[:5]):
                print(f"  {i+1}. Date: {point.get('date', 'N/A')}, Price: {point.get('price', 'N/A')}")
            
            if len(self.data_points) > 5:
                print(f"  ... and {len(self.data_points) - 5} more")


async def main():
    """
    Main function to run the scraper
    """
    print("Kline Chart Data Scraper")
    print("=" * 30)
    
    scraper = KlineChartScraper()
    
    # Run the scraper
    await scraper.scrape_chart_data(headless=False)  # Set to True for headless mode
    
    # Print summary
    scraper.print_summary()
    
    # Save data
    if scraper.data_points:
        scraper.save_to_csv()
        scraper.save_to_json()
    else:
        print("No data extracted. You may need to adjust the selectors or approach.")


if __name__ == "__main__":
    # Install required packages first:
    # pip install playwright
    # playwright install
    
    asyncio.run(main())