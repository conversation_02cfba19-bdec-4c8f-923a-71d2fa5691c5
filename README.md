# Kline Shipping Data Extractor

This project extracts comprehensive shipping market data from the Kline website.

## 📁 Project Structure

```
shipment/
├── data/                           # All extracted data
│   ├── shipping_indices/          # Main shipping indices (BSI, BDI, etc.)
│   ├── charts_data/               # Organized by chart with metadata
│   └── raw_extracts/              # Raw JSON data and page source
├── scripts/                       # Data extraction scripts
│   ├── kline_complete_data_extractor.py  # ⭐ MAIN SCRIPT (only one you need)
│   └── old_scripts/               # All other scripts (legacy/experimental)
├── documentation/                 # Project documentation
├── manual/                        # Manual calculations and reference files
├── old/                          # Legacy scripts
└── temp_files/                   # Temporary JavaScript files
```

## 🚢 Available Shipping Indices

### Baltic Indices
- **BSI** (Baltic Supramax Index) - Main index for Supramax vessels
- **BDI** (Baltic Dry Index) - Overall dry bulk market
- **BCI** (Baltic Capesize Index) - Capesize vessels
- **BPI** (Baltic Panamax Index) - Panamax vessels  
- **BHSI** (Baltic Handysize Index) - Handysize vessels

### Tanker Indices
- **VLCC** (Very Large Crude Carrier)
- **Aframax** - Medium-sized tankers

### Container Indices
- **SCFI** (Shanghai Containerized Freight Index)
  - USWC (US West Coast)
  - USEC (US East Coast)
  - N.EUR (North Europe)
  - MED (Mediterranean)

## 🔧 Usage

### Extract All Data
```bash
python scripts/kline_complete_data_extractor.py
```

This will:
1. Load the Kline shipping page
2. Extract data from all 11 charts
3. Organize data into structured folders
4. Generate summary reports

### Data Format
All shipping indices are saved as CSV files with columns:
- `date` - Date in YYYY/M/D format
- `value` - Index value

## 📊 Data Coverage

- **Time Range**: 2015 to 2025 (includes projections)
- **Frequency**: Weekly data points
- **Total Charts**: 11 different shipping market charts
- **Data Points**: 500+ per major index

## 🎯 Key Files

- `data/shipping_indices/BSI_data.csv` - Baltic Supramax Index
- `data/extraction_summary.json` - Complete extraction summary
- `data/charts_data/` - Individual chart folders with metadata

## 📈 Data Quality

✅ **High Accuracy**: Data extracted directly from chart runtime  
✅ **Complete Coverage**: All available historical data  
✅ **Structured Format**: Ready for analysis and calculations  
✅ **Metadata Included**: Chart information and data descriptions  

## 🔄 Updates

To get the latest data, simply run the main extractor script again. It will overwrite existing files with fresh data from the website.

## 📋 Notes

- Data is extracted from live charts, ensuring accuracy
- Future dates may include projections/forecasts
- All timestamps are in the source website's timezone
- Some series may have gaps for weekends/holidays
