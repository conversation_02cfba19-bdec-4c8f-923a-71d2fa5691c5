import os
import shutil
from pathlib import Path

def cleanup_and_organize():
    """
    Clean up the current directory and organize files into proper folders
    """
    
    # Create organized folder structure
    folders_to_create = [
        'data',
        'data/shipping_indices',
        'data/raw_extracts', 
        'data/charts_data',
        'scripts',
        'scripts/old_scripts',
        'temp_files',
        'documentation'
    ]
    
    for folder in folders_to_create:
        Path(folder).mkdir(parents=True, exist_ok=True)
        print(f"Created folder: {folder}")
    
    # File organization mapping
    file_moves = {
        # CSV data files to shipping_indices
        'data/shipping_indices': [
            'bsi_index_data.csv',
            'bsi_index_extracted.csv', 
            'bdi_index_data.csv',
            'bci_index_data.csv',
            'bpi_index_data.csv',
            'bhsi_index_data.csv',
            'aframax_index_data.csv',
            'vlcc_index_data.csv'
        ],
        
        # Raw data files
        'data/raw_extracts': [
            'highcharts_runtime_data.json',
            'all_requests.json',
            'all_responses.json',
            'page_source.html'
        ],
        
        # Current working scripts
        'scripts': [
            'analyze_data_source.py',
            'extract_bsi_from_chart_data.py'
        ],
        
        # Old/experimental scripts
        'scripts/old_scripts': [
            'extract_chart_tooltips.py',
            'simple_chart_test.py',
            'parse_bsi_data.py',
            'kline_scraper.py'
        ],
        
        # Temporary files
        'temp_files': [
            f for f in os.listdir('.') if f.startswith('script_') and f.endswith('.js')
        ],
        
        # Documentation
        'documentation': [
            'BSI_DATA_EXTRACTION_SUMMARY.md'
        ]
    }
    
    # Move files to organized folders
    for destination, files in file_moves.items():
        for file in files:
            if os.path.exists(file):
                try:
                    shutil.move(file, os.path.join(destination, file))
                    print(f"Moved {file} to {destination}/")
                except Exception as e:
                    print(f"Error moving {file}: {e}")
    
    # Keep manual and old folders as they are (already organized)
    print("\nCleanup complete!")
    print("\nNew folder structure:")
    for folder in folders_to_create:
        if os.path.exists(folder):
            files = os.listdir(folder)
            print(f"  {folder}/ ({len(files)} files)")
            for file in files[:3]:  # Show first 3 files
                print(f"    - {file}")
            if len(files) > 3:
                print(f"    ... and {len(files) - 3} more")

if __name__ == "__main__":
    cleanup_and_organize()
