import csv

filename = "freight_supplier_price_check.csv"

rows = [
    ["Input/Label", "Value / User Input", "Notes"],
    ["FOB Price (USD/ton)", "470", "Origin steel price"],
    ["Cargo Size (tons)", "30000", "Shipment cargo size"],
    ["BSI Index (TCE/day)", "1007", "Baltic Supramax Index"],
    ["Round Trip Days", "55", "Your route round-trip days"],
    ["Unloading Fee (USD/ton)", "30", "Port unloading fee"],
    ["Local Logistics (USD/ton)", "10", "Transport, storage"],
    ["Supplier Price (USD/ton)", "600", "Price supplier charges"],
    [],
    ["Calculations:", "", ""],
    ["TCE per day (USD)", "=B4 * 12", "Approximation: BSI x 12k"],
    ["Freight cost (USD/ton)", "=(B11 * B5) / B3", "=(TCE per day * round trip days) / cargo size"],
    ["Total Landed Cost (USD/ton)", "=B2 + B12 + B6 + B7", "FOB + freight + unloading + logistics"],
    ["Max Fair Supplier Price (USD/ton)", "=B13 * 1.10", "10% margin on total cost"],
    ["Supplier Margin (USD/ton)", "=B8 - B14", "Difference supplier price vs max fair price"],
]

with open(filename, "w", newline="") as file:
    writer = csv.writer(file)
    writer.writerows(rows)

filename
