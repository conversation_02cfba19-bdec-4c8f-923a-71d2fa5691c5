from playwright.sync_api import sync_playwright
import time

def test_chart_interaction():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()
        
        print("Loading page...")
        page.goto('https://www.kline.co.jp/en/ir/finance/shipping.html', wait_until='networkidle')
        page.wait_for_timeout(5000)
        
        # Take a screenshot to see what we're working with
        page.screenshot(path='chart_page.png')
        print("Screenshot saved as chart_page.png")
        
        # Find all elements with 'chart' in their class or id
        chart_elements = page.query_selector_all('[class*="chart"], [id*="chart"], [class*="highcharts"], svg')
        print(f"Found {len(chart_elements)} potential chart elements")
        
        for i, elem in enumerate(chart_elements):
            try:
                tag = elem.evaluate('el => el.tagName')
                classes = elem.get_attribute('class') or ''
                elem_id = elem.get_attribute('id') or ''
                bbox = elem.bounding_box()
                
                if bbox and bbox['width'] > 100 and bbox['height'] > 100:
                    print(f"Element {i}: {tag}, classes='{classes}', id='{elem_id}', size={bbox['width']}x{bbox['height']}")
                    
                    # Try hovering over this element
                    center_x = bbox['x'] + bbox['width'] / 2
                    center_y = bbox['y'] + bbox['height'] / 2
                    
                    print(f"  Hovering at ({center_x}, {center_y})")
                    page.mouse.move(center_x, center_y)
                    page.wait_for_timeout(1000)
                    
                    # Check for any new elements that appeared
                    all_visible = page.query_selector_all('*')
                    tooltip_candidates = []
                    
                    for candidate in all_visible:
                        try:
                            if candidate.is_visible():
                                text = candidate.evaluate('el => el.textContent || ""')
                                if text and len(text.strip()) > 0 and len(text) < 300:
                                    # Check if it contains numbers that could be BSI values
                                    if any(char.isdigit() for char in text) and ('20' in text or 'BSI' in text.upper() or len([c for c in text if c.isdigit()]) > 3):
                                        tooltip_candidates.append(text.strip())
                        except:
                            continue
                    
                    if tooltip_candidates:
                        print(f"  Potential tooltips found:")
                        for candidate in tooltip_candidates[:3]:  # Show first 3
                            print(f"    - {candidate[:100]}")
                    else:
                        print(f"  No tooltips found")
                        
            except Exception as e:
                print(f"Error with element {i}: {e}")
        
        # Let's also try to find the specific path element you mentioned
        print("\nLooking for the specific path element...")
        path_elements = page.query_selector_all('path')
        print(f"Found {len(path_elements)} path elements")
        
        for i, path in enumerate(path_elements):
            try:
                d_attr = path.get_attribute('d')
                stroke_width = path.get_attribute('stroke-width')
                classes = path.get_attribute('class') or ''
                
                if d_attr and 'M -9' in d_attr[:20]:  # Your path starts with "M -9.390384615384615"
                    print(f"Found target path element {i}:")
                    print(f"  stroke-width: {stroke_width}")
                    print(f"  classes: {classes}")
                    print(f"  d starts with: {d_attr[:50]}...")
                    
                    # Try to hover over different points of this path
                    bbox = path.bounding_box()
                    if bbox:
                        print(f"  Path bounding box: {bbox}")
                        
                        # Try hovering over several points along the path
                        test_points = [
                            (bbox['x'] + bbox['width'] * 0.2, bbox['y'] + bbox['height'] * 0.5),
                            (bbox['x'] + bbox['width'] * 0.5, bbox['y'] + bbox['height'] * 0.5),
                            (bbox['x'] + bbox['width'] * 0.8, bbox['y'] + bbox['height'] * 0.5),
                        ]
                        
                        for j, (x, y) in enumerate(test_points):
                            print(f"    Testing point {j+1}: ({x}, {y})")
                            page.mouse.move(x, y)
                            page.wait_for_timeout(500)
                            
                            # Look for tooltips
                            tooltip_selectors = [
                                '.highcharts-tooltip',
                                '[class*="tooltip"]',
                                '[style*="position: absolute"]',
                                '[style*="z-index"]'
                            ]
                            
                            for selector in tooltip_selectors:
                                tooltips = page.query_selector_all(selector)
                                for tooltip in tooltips:
                                    try:
                                        if tooltip.is_visible():
                                            text = tooltip.evaluate('el => el.textContent || el.innerText || ""')
                                            if text and text.strip():
                                                print(f"      Tooltip found: {text.strip()}")
                                    except:
                                        continue
                    
            except Exception as e:
                print(f"Error with path {i}: {e}")
        
        print("\nTest complete. Check chart_page.png to see the page.")
        input("Press Enter to close browser...")
        browser.close()

if __name__ == "__main__":
    test_chart_interaction()
