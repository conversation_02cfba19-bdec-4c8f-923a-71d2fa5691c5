import requests
import json
import csv
from datetime import datetime

# TODO: Replace with actual API endpoint once identified
API_URL = "https://api.example.com/bsi-data"  

def fetch_bsi_data():
    try:
        response = requests.get(API_URL)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data: {e}")
        return None

def process_data(json_data):
    """Process raw JSON data into structured format"""
    # TODO: Adjust based on actual API response structure
    processed = []
    for item in json_data.get('data', []):
        processed.append({
            'date': item.get('date'),
            'value': item.get('value'),
            'change': item.get('change')
        })
    return processed

def save_to_csv(data, filename):
    """Save processed data to CSV"""
    with open(filename, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=['date', 'value', 'change'])
        writer.writeheader()
        writer.writerows(data)

if __name__ == "__main__":
    print("BSI Data Extractor")
    print("1. Fetching data...")
    raw_data = fetch_bsi_data()
    
    if raw_data:
        print("2. Processing data...")
        processed_data = process_data(raw_data)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"bsi_index_{timestamp}.csv"
        
        print(f"3. Saving to {output_file}...")
        save_to_csv(processed_data, output_file)
        print("Done!")
    else:
        print("Failed to fetch data")
