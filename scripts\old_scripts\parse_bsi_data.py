import re
from datetime import datetime, timedelta

def parse_svg_path(svg_path):
    # Extract all coordinates from the path
    coords = re.findall(r'L\s([\d.-]+)\s([\d.-]+)', svg_path)
    
    # The first data point starts from 2015 (10 years of monthly data)
    start_date = datetime(2015, 1, 1)
    
    # Convert coordinates to date-value pairs
    data = []
    for i, (x, y) in enumerate(coords):
        date = start_date + timedelta(days=30.44*i)  # Approximate monthly intervals
        value = float(y)
        data.append({
            'date': date.strftime('%Y-%m'),
            'bsi_index': value
        })
    
    return data

if __name__ == '__main__':
    # The SVG path from the original task
    svg_path = """M -9.003846153846155 258.1267 L 0.9961538461538462 258.1267 L 2.9884615384615385 257.5287..."""  # truncated
    
    bsi_data = parse_svg_path(svg_path)
    
    # Save to CSV
    with open('bsi_index_10years.csv', 'w') as f:
        f.write('date,bsi_index\n')
        for point in bsi_data:
            f.write(f"{point['date']},{point['bsi_index']}\n")
    
    print(f"Successfully parsed {len(bsi_data)} data points")
