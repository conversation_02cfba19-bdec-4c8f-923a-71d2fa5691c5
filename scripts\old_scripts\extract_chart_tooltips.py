from playwright.sync_api import sync_playwright
import json
import csv
import time
from datetime import datetime
import re

def extract_highcharts_tooltip_data():
    """
    Extract BSI data from Highcharts by hovering over the chart line and capturing tooltips
    """
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()

        print("Navigating to BSI chart page...")
        page.goto('https://www.kline.co.jp/en/ir/finance/shipping.html', wait_until='networkidle')
        page.wait_for_timeout(5000)  # Wait for chart to fully load

        # Find the Highcharts container
        highcharts_container = page.query_selector('.highcharts-container')
        if not highcharts_container:
            print("Highcharts container not found!")
            browser.close()
            return []

        print("Found Highcharts container")

        # Find the chart series path (the line we want to hover over)
        series_path = page.query_selector('path[stroke-width="22"]')  # The tracker path from your example
        if not series_path:
            # Try alternative selectors
            series_path = page.query_selector('.highcharts-tracker')
            if not series_path:
                series_path = page.query_selector('path[d*="M -9"]')  # Based on your path data

        if not series_path:
            print("Could not find the chart series path!")
            browser.close()
            return []

        print("Found chart series path")

        # Get the path data and parse coordinates
        path_data = series_path.get_attribute('d')
        if not path_data:
            print("Could not get path data!")
            browser.close()
            return []

        # Extract coordinates from the path
        coords = re.findall(r'L\s*([-\d.]+)\s*([-\d.]+)', path_data)
        if not coords:
            # Try alternative pattern for the first M command
            coords = re.findall(r'[ML]\s*([-\d.]+)\s*([-\d.]+)', path_data)

        print(f"Found {len(coords)} coordinate points in the path")

        # Get the chart container's position for coordinate transformation
        chart_box = highcharts_container.bounding_box()
        if not chart_box:
            print("Could not get chart bounding box")
            browser.close()
            return []

        print(f"Chart container: x={chart_box['x']}, y={chart_box['y']}, width={chart_box['width']}, height={chart_box['height']}")

        extracted_data = []
        tooltip_data = []

        # Function to capture Highcharts tooltip
        def capture_highcharts_tooltip():
            try:
                # Wait a bit for tooltip to appear
                page.wait_for_timeout(300)

                # Highcharts tooltip selectors
                tooltip_selectors = [
                    '.highcharts-tooltip',
                    '.highcharts-label.highcharts-tooltip',
                    '[class*="highcharts-tooltip"]',
                    '.highcharts-tooltip-container'
                ]

                for selector in tooltip_selectors:
                    tooltips = page.query_selector_all(selector)
                    for tooltip in tooltips:
                        try:
                            if tooltip.is_visible():
                                # Try to get text content safely
                                tooltip_text = tooltip.text_content()
                                if not tooltip_text:
                                    tooltip_text = tooltip.inner_text()

                                if tooltip_text and tooltip_text.strip():
                                    tooltip_html = tooltip.inner_html()
                                    return {
                                        'text': tooltip_text.strip(),
                                        'html': tooltip_html
                                    }
                        except Exception as inner_e:
                            # Try alternative method
                            try:
                                tooltip_text = tooltip.evaluate('el => el.textContent || el.innerText')
                                if tooltip_text and tooltip_text.strip():
                                    return {
                                        'text': tooltip_text.strip(),
                                        'html': 'N/A'
                                    }
                            except:
                                continue

                # Check for any visible elements that might be tooltips
                all_visible = page.query_selector_all('[style*="display: block"], [style*="opacity: 1"]')
                for elem in all_visible:
                    try:
                        if elem.is_visible():
                            text = elem.evaluate('el => el.textContent || el.innerText || ""')
                            if text and text.strip() and len(text) < 500:
                                # Check if it looks like tooltip content
                                if any(keyword in text.lower() for keyword in ['bsi', 'index', '20', 'date']):
                                    return {
                                        'text': text.strip(),
                                        'html': 'N/A'
                                    }
                    except:
                        continue

            except Exception as e:
                print(f"Error capturing tooltip: {e}")
            return None

        # Sample points along the path - we'll take every 5th point to avoid too many requests
        sample_coords = coords[::5] if len(coords) > 50 else coords
        print(f"Sampling {len(sample_coords)} points from the path")

        for i, (x_str, y_str) in enumerate(sample_coords):
            try:
                # Convert path coordinates to screen coordinates
                path_x = float(x_str)
                path_y = float(y_str)

                # Transform to screen coordinates (this is approximate)
                screen_x = chart_box['x'] + path_x + (chart_box['width'] / 2)
                screen_y = chart_box['y'] + path_y

                # Make sure coordinates are within the chart area
                if (chart_box['x'] <= screen_x <= chart_box['x'] + chart_box['width'] and
                    chart_box['y'] <= screen_y <= chart_box['y'] + chart_box['height']):

                    # Move mouse to the point
                    page.mouse.move(screen_x, screen_y)
                    page.wait_for_timeout(200)  # Wait for tooltip to appear

                    # Capture tooltip
                    tooltip_info = capture_highcharts_tooltip()
                    if tooltip_info:
                        tooltip_data.append({
                            'path_coords': (path_x, path_y),
                            'screen_coords': (screen_x, screen_y),
                            'tooltip': tooltip_info,
                            'index': i
                        })
                        print(f"Point {i+1}/{len(sample_coords)}: Captured tooltip - {tooltip_info['text'][:50]}...")
                    else:
                        if i % 10 == 0:  # Progress update for points without tooltips
                            print(f"Point {i+1}/{len(sample_coords)}: No tooltip")

            except Exception as e:
                print(f"Error processing point {i}: {e}")
                continue

        print(f"\nCaptured {len(tooltip_data)} tooltips")

        # Process tooltip data to extract structured information
        for tooltip_item in tooltip_data:
            tooltip_text = tooltip_item['tooltip']['text']

            # Parse Highcharts tooltip format
            # Common formats: "Date: Jan 1, 2023\nValue: 1234.56" or "Jan 1, 2023: 1234.56"
            date_found = None
            value_found = None

            # Try different parsing strategies
            lines = tooltip_text.split('\n')

            for line in lines:
                # Look for date patterns
                date_patterns = [
                    r'(\w+ \d{1,2}, \d{4})',  # "Jan 1, 2023"
                    r'(\d{4}-\d{2}-\d{2})',   # "2023-01-01"
                    r'(\d{1,2}/\d{1,2}/\d{4})', # "1/1/2023"
                ]

                for pattern in date_patterns:
                    match = re.search(pattern, line)
                    if match:
                        date_found = match.group(1)
                        break

                # Look for numeric values
                value_matches = re.findall(r'(\d+\.?\d*)', line)
                if value_matches:
                    # Take the largest number (likely the main value)
                    numbers = [float(x) for x in value_matches if float(x) > 10]  # Filter out small numbers like years
                    if numbers:
                        value_found = max(numbers)

            if date_found or value_found:
                extracted_data.append({
                    'date': date_found,
                    'value': value_found,
                    'raw_tooltip': tooltip_text,
                    'path_coords': tooltip_item['path_coords'],
                    'screen_coords': tooltip_item['screen_coords']
                })

        # Save all data
        with open('highcharts_tooltips.json', 'w') as f:
            json.dump(tooltip_data, f, indent=2, default=str)

        with open('bsi_extracted_data.json', 'w') as f:
            json.dump(extracted_data, f, indent=2, default=str)

        if extracted_data:
            with open('bsi_tooltip_data.csv', 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=['date', 'value', 'raw_tooltip', 'path_coords', 'screen_coords'])
                writer.writeheader()
                writer.writerows(extracted_data)

        browser.close()

        print(f"\nExtraction complete!")
        print(f"Raw tooltip data: highcharts_tooltips.json")
        print(f"Structured data: bsi_extracted_data.json")
        if extracted_data:
            print(f"CSV data: bsi_tooltip_data.csv")
            print(f"Successfully extracted {len(extracted_data)} structured data points")

        return extracted_data

def quick_test_tooltip():
    """
    Quick test to see if we can capture a single tooltip
    """
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()

        print("Quick tooltip test...")
        page.goto('https://www.kline.co.jp/en/ir/finance/shipping.html', wait_until='networkidle')
        page.wait_for_timeout(5000)

        # Find chart and hover over center
        chart = page.query_selector('.highcharts-container')
        if chart:
            chart_box = chart.bounding_box()
            center_x = chart_box['x'] + chart_box['width'] / 2
            center_y = chart_box['y'] + chart_box['height'] / 2

            print(f"Hovering over chart center: ({center_x}, {center_y})")
            page.mouse.move(center_x, center_y)
            page.wait_for_timeout(1000)

            # Check for tooltip using safer method
            try:
                tooltip = page.query_selector('.highcharts-tooltip')
                if tooltip and tooltip.is_visible():
                    tooltip_text = tooltip.evaluate('el => el.textContent || el.innerText || "No text"')
                    print(f"Tooltip found: {tooltip_text}")
                else:
                    print("No tooltip visible - checking all visible elements...")
                    # Check all visible elements for potential tooltips
                    all_elements = page.query_selector_all('[style*="display: block"], [style*="opacity"]')
                    for elem in all_elements[:10]:  # Check first 10
                        try:
                            if elem.is_visible():
                                text = elem.evaluate('el => el.textContent || el.innerText || ""')
                                if text and len(text) < 200 and any(c.isdigit() for c in text):
                                    print(f"Potential tooltip: {text[:100]}")
                        except:
                            continue
            except Exception as e:
                print(f"Error checking tooltip: {e}")
        else:
            print("Chart container not found")

        browser.close()

if __name__ == "__main__":
    print("BSI Highcharts Data Extractor")
    print("=" * 50)

    # Quick test first
    print("Step 1: Quick tooltip test...")
    quick_test_tooltip()

    print("\nStep 2: Full data extraction...")
    extracted_data = extract_highcharts_tooltip_data()

    if extracted_data:
        print(f"\nSuccess! Extracted {len(extracted_data)} data points")
        print("Files created:")
        print("- highcharts_tooltips.json (raw tooltip data)")
        print("- bsi_extracted_data.json (structured data)")
        print("- bsi_tooltip_data.csv (CSV format)")

        # Show sample of extracted data
        if len(extracted_data) > 0:
            print(f"\nSample data point:")
            sample = extracted_data[0]
            print(f"Date: {sample.get('date', 'N/A')}")
            print(f"Value: {sample.get('value', 'N/A')}")
            print(f"Raw tooltip: {sample.get('raw_tooltip', 'N/A')[:100]}...")
    else:
        print("\nNo structured data extracted. Check the raw tooltip files for debugging.")
