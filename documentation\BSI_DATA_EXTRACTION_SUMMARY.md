# BSI Data Extraction Summary

## Success! 🎉

We successfully extracted the **Baltic Supramax Index (BSI)** data directly from the Kline website's interactive chart by analyzing how the webpage works and accessing the chart's data source.

## What We Discovered

Instead of trying to scrape the visual chart or parse SVG paths, we found that the webpage uses **Highcharts** to display the data, and we could access the actual data directly from the JavaScript runtime.

## Data Source Analysis

The webpage (https://www.kline.co.jp/en/ir/finance/shipping.html) contains multiple shipping indices charts:

1. **Dry Bulk Market Index (BDI)** - Baltic Dry Index
2. **Dry Bulk Market Index (BCI/BPI/BSI/BHSI)** - Multiple Baltic indices including:
   - BCI (Baltic Capesize Index)
   - BPI (Baltic Panamax Index) 
   - **BSI (Baltic Supramax Index)** ← This is what we needed!
   - BHSI (Baltic Handysize Index)
3. Tanker Market Freight Index
4. Container Market Freight Index
5. And several other shipping-related charts

## Extracted BSI Data

### Data Range
- **Start Date**: 2015/6/5
- **End Date**: 2025/6/27 (future projections included)
- **Total Data Points**: 519 weekly data points
- **Time Span**: ~10 years of data

### BSI Index Statistics
- **Minimum Value**: 2,591
- **Maximum Value**: 39,666
- **Average Value**: 12,628.31

### Sample Data Points
```
Date        BSI Index
2015/6/5    6,835
2015/6/12   6,935
2015/6/19   7,364
...
2025/6/20   10,026
2025/6/27   10,467
```

## Files Created

1. **`bsi_index_extracted.csv`** - BSI data with date, BSI index value, and x-index
2. **`bsi_index_data.csv`** - Clean BSI data with just date and value
3. **`bdi_index_data.csv`** - Baltic Dry Index data
4. **`bci_index_data.csv`** - Baltic Capesize Index data
5. **`bpi_index_data.csv`** - Baltic Panamax Index data
6. **`bhsi_index_data.csv`** - Baltic Handysize Index data
7. **`highcharts_runtime_data.json`** - Complete raw chart data from all charts

## Technical Approach

### Method Used: Runtime Data Extraction
Instead of visual scraping, we:

1. **Analyzed the webpage structure** using Playwright browser automation
2. **Monitored network requests** to understand data flow
3. **Accessed Highcharts runtime data** directly from the JavaScript execution context
4. **Extracted structured data** from the chart instances
5. **Processed and cleaned the data** into CSV format

### Key Script: `analyze_data_source.py`
This script:
- Opens the webpage in a browser
- Captures all network requests
- Analyzes the page source for embedded data
- Extracts data directly from Highcharts instances using JavaScript execution
- Saves all chart data to JSON format

### Data Processing: `extract_bsi_from_chart_data.py`
This script:
- Parses the extracted chart data
- Identifies the BSI series specifically
- Converts to clean CSV format
- Provides data statistics and validation

## Data Quality

✅ **High Quality Data**: The extracted data comes directly from the chart's data source, ensuring:
- Accurate values (no approximation from visual parsing)
- Correct dates (no date estimation)
- Complete dataset (all available data points)
- Proper formatting (ready for analysis)

## Usage for Your Freight Calculations

The BSI data is now ready to use in your freight cost calculations. The current BSI value (as of the latest data point) is **10,467** for 2025/6/27.

For your freight calculations, you can:
1. Use the latest BSI value for current calculations
2. Analyze historical trends for forecasting
3. Calculate averages for different time periods
4. Track BSI volatility for risk assessment

## Comparison with Previous Approach

| Method | SVG Path Parsing | Runtime Data Extraction |
|--------|------------------|-------------------------|
| Accuracy | Approximate | Exact |
| Date Precision | Estimated | Exact |
| Data Completeness | Partial | Complete |
| Maintenance | High (brittle) | Low (robust) |
| Data Quality | Medium | High |

## Next Steps

You can now use the BSI data in your freight cost calculations. The data is in CSV format and ready for:
- Excel analysis
- Python/pandas processing  
- Database import
- API integration

The approach we developed can also be used to automatically update the BSI data by running the extraction scripts periodically.
