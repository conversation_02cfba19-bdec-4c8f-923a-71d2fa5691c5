import csv

filename_updated = "freight_supplier_price_check_with_route.csv"

updated_rows = [
    ["Input/Label", "Value / User Input", "Notes / Explanation"],
    ["Origin Country", "China", "Country of shipment origin"],
    ["Typical Port of Loading", "Tianjin, Rizhao, Shanghai", "Major loading ports in origin country"],
    ["Route Description", "South China Sea → Indian Ocean → Suez → Med", "Shipping route description"],
    ["Destination Port (Israel)", "Ashdod / Haifa", "Destination port in Israel"],
    ["Distance (NM)", "10500", "Nautical miles, average"],
    ["Laden Voyage Days", "14", "Laden leg (loaded ship)"],
    ["Ballast Voyage Days", "10", "Ballast leg (empty ship return)"],
    ["Port Time (days)", "5", "Time spent in ports (loading/unloading)"],
    ["Voyage Days (Total)", "=B7+B8+B9", "Sum of Laden + Ballast + Port Time"],
    [],
    ["FOB Price (USD/ton)", "470", "Origin steel price"],
    ["Cargo Size (tons)", "30000", "Shipment cargo size"],
    ["BSI Index (TCE/day)", "1007", "Baltic Supramax Index"],
    ["Unloading Fee (USD/ton)", "30", "Port unloading fee"],
    ["Local Logistics (USD/ton)", "10", "Transport, storage"],
    ["Supplier Price (USD/ton)", "600", "Price supplier charges"],
    [],
    ["Calculations:", "", ""],
    ["TCE per day (USD)", "=B14 * 12", "Approximation: BSI x 12k"],
    ["Freight cost (USD/ton)", "=(B15 * B10) / B3", "(TCE per day * voyage days total) / cargo size"],
    ["Total Landed Cost (USD/ton)", "=B11 + B16 + B5 + B6", "FOB + freight + unloading + logistics"],
    ["Max Fair Supplier Price (USD/ton)", "=B17 * 1.10", "10% margin on total cost"],
    ["Supplier Margin (USD/ton)", "=B7 - B18", "Difference supplier price vs max fair price"],
]

with open(filename_updated, "w", newline="") as file:
    writer = csv.writer(file)
    writer.writerows(updated_rows)

filename_updated
