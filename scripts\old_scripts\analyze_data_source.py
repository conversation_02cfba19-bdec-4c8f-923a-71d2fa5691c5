from playwright.sync_api import sync_playwright
import json
import re

def analyze_chart_data_source():
    """
    Analyze how the BSI chart gets its data by monitoring network requests
    and examining the page source
    """
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()
        
        # Collect all network requests
        requests = []
        responses = []
        
        def log_request(request):
            requests.append({
                'url': request.url,
                'method': request.method,
                'headers': dict(request.headers),
                'post_data': request.post_data,
                'resource_type': request.resource_type
            })
        
        def log_response(response):
            responses.append({
                'url': response.url,
                'status': response.status,
                'headers': dict(response.headers),
                'request': {
                    'method': response.request.method,
                    'url': response.request.url
                }
            })
        
        page.on('request', log_request)
        page.on('response', log_response)
        
        print("Loading page and monitoring network requests...")
        page.goto('https://www.kline.co.jp/en/ir/finance/shipping.html', wait_until='networkidle')
        page.wait_for_timeout(5000)  # Wait for any additional requests
        
        print(f"Captured {len(requests)} requests and {len(responses)} responses")
        
        # Analyze requests for potential data sources
        print("\n=== ANALYZING REQUESTS ===")
        data_requests = []
        
        for req in requests:
            url = req['url']
            # Look for potential data endpoints
            if any(keyword in url.lower() for keyword in ['api', 'data', 'json', 'csv', 'xml', 'ajax']):
                data_requests.append(req)
                print(f"Data request: {req['method']} {url}")
            elif any(keyword in url.lower() for keyword in ['bsi', 'shipping', 'index', 'chart']):
                data_requests.append(req)
                print(f"BSI-related request: {req['method']} {url}")
        
        # Save all requests for detailed analysis
        with open('all_requests.json', 'w') as f:
            json.dump(requests, f, indent=2)
        
        with open('all_responses.json', 'w') as f:
            json.dump(responses, f, indent=2)
        
        # Examine page source for embedded data
        print("\n=== ANALYZING PAGE SOURCE ===")
        page_content = page.content()
        
        # Look for JavaScript variables that might contain chart data
        js_patterns = [
            r'var\s+\w*[Dd]ata\w*\s*=\s*(\[.*?\]);',
            r'let\s+\w*[Dd]ata\w*\s*=\s*(\[.*?\]);',
            r'const\s+\w*[Dd]ata\w*\s*=\s*(\[.*?\]);',
            r'chartData\s*[:=]\s*(\[.*?\])',
            r'series\s*[:=]\s*(\[.*?\])',
            r'data\s*[:=]\s*(\[.*?\])',
        ]
        
        embedded_data = []
        for pattern in js_patterns:
            matches = re.findall(pattern, page_content, re.DOTALL)
            for match in matches:
                if len(match) > 100:  # Significant data
                    embedded_data.append(match)
                    print(f"Found embedded data: {match[:100]}...")
        
        # Look for Highcharts configuration
        highcharts_patterns = [
            r'Highcharts\.chart\([^,]+,\s*({.*?})\);',
            r'new\s+Highcharts\.Chart\([^,]+,\s*({.*?})\);',
            r'\.highcharts\(({.*?})\)',
        ]
        
        chart_configs = []
        for pattern in highcharts_patterns:
            matches = re.findall(pattern, page_content, re.DOTALL)
            for match in matches:
                chart_configs.append(match)
                print(f"Found Highcharts config: {match[:200]}...")
        
        # Save page source for manual inspection
        with open('page_source.html', 'w', encoding='utf-8') as f:
            f.write(page_content)
        
        # Look for script tags that might load data
        print("\n=== ANALYZING SCRIPT TAGS ===")
        script_elements = page.query_selector_all('script')
        
        for i, script in enumerate(script_elements):
            try:
                src = script.get_attribute('src')
                if src:
                    print(f"External script {i}: {src}")
                else:
                    content = script.inner_text()
                    if content and len(content) > 100:
                        # Look for data-related content
                        if any(keyword in content.lower() for keyword in ['bsi', 'chart', 'data', 'series']):
                            print(f"Inline script {i} contains chart-related code: {content[:200]}...")
                            
                            # Save this script for detailed analysis
                            with open(f'script_{i}.js', 'w', encoding='utf-8') as f:
                                f.write(content)
            except Exception as e:
                print(f"Error analyzing script {i}: {e}")
        
        # Try to extract data from JavaScript execution context
        print("\n=== EXTRACTING RUNTIME DATA ===")
        try:
            # Try to access Highcharts instances
            charts_data = page.evaluate('''
                () => {
                    if (typeof Highcharts !== 'undefined' && Highcharts.charts) {
                        return Highcharts.charts.map((chart, index) => {
                            if (chart && chart.series) {
                                return {
                                    index: index,
                                    title: chart.title ? chart.title.textStr : 'No title',
                                    series: chart.series.map(s => ({
                                        name: s.name,
                                        data: s.data ? s.data.map(point => ({
                                            x: point.x,
                                            y: point.y,
                                            category: point.category
                                        })) : []
                                    }))
                                };
                            }
                            return null;
                        }).filter(chart => chart !== null);
                    }
                    return [];
                }
            ''')
            
            if charts_data:
                print(f"Found {len(charts_data)} Highcharts instances with data!")
                with open('highcharts_runtime_data.json', 'w') as f:
                    json.dump(charts_data, f, indent=2)
                
                # Look for BSI data specifically
                for chart in charts_data:
                    print(f"Chart: {chart['title']}")
                    for series in chart['series']:
                        if series['data']:
                            print(f"  Series '{series['name']}': {len(series['data'])} data points")
                            if len(series['data']) > 0:
                                sample = series['data'][0]
                                print(f"    Sample point: {sample}")
            else:
                print("No Highcharts data found in runtime")
                
        except Exception as e:
            print(f"Error extracting runtime data: {e}")
        
        print(f"\n=== SUMMARY ===")
        print(f"Total requests: {len(requests)}")
        print(f"Data-related requests: {len(data_requests)}")
        print(f"Embedded data found: {len(embedded_data)}")
        print(f"Chart configs found: {len(chart_configs)}")
        
        print(f"\nFiles created:")
        print(f"- all_requests.json (all network requests)")
        print(f"- all_responses.json (all responses)")
        print(f"- page_source.html (full page source)")
        if charts_data:
            print(f"- highcharts_runtime_data.json (extracted chart data)")
        
        input("Press Enter to close browser...")
        browser.close()
        
        return {
            'requests': requests,
            'responses': responses,
            'embedded_data': embedded_data,
            'chart_configs': chart_configs,
            'charts_data': charts_data if 'charts_data' in locals() else []
        }

if __name__ == "__main__":
    print("BSI Chart Data Source Analyzer")
    print("=" * 50)
    result = analyze_chart_data_source()
    
    if result['charts_data']:
        print(f"\nSuccess! Found chart data with {len(result['charts_data'])} charts")
        for i, chart in enumerate(result['charts_data']):
            total_points = sum(len(series['data']) for series in chart['series'])
            print(f"Chart {i}: '{chart['title']}' - {total_points} total data points")
    else:
        print("\nNo direct chart data found. Check the request logs for API endpoints.")
