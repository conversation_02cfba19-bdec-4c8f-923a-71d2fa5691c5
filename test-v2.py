import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import seaborn as sns
import os
import numpy as np

# 1. Load BSI Data from Local CSV
bsi_path = Path(r"C:\Users\<USER>\Code\shipment\data\shipping_indices\BSI_data.csv")
bsi_df = pd.read_csv(bsi_path)
bsi_df['date'] = pd.to_datetime(bsi_df['date'], format='mixed', dayfirst=True)
bsi_df.set_index('date', inplace=True)

# 2. Data Validation
print(f"Data Range: {bsi_df.index.min().date()} to {bsi_df.index.max().date()}")
print(f"Missing Values: {bsi_df.isnull().sum()}")

# 3. Enhanced Freight Cost Calculator for Breakbulk
class BreakbulkFreightCalculator:
    def __init__(self, bsi_data):
        self.bsi = bsi_data
        self.vessel_capacity = 50000  # Standard Supramax DWT
        # Breakbulk specific adjustments
        self.breakbulk_factors = {
            'China': 1.45,  # 45% premium over bulk rates
            'Turkey': 1.35,
            'Russia': 1.4,
            'Israel': 1.0
        }
        self.port_fees = {
            'China': 22,  # Higher for breakbulk handling
            'Turkey': 18,
            'Russia': 20,
            'Israel': 15
        }
        self.route_distances = {
            'China_Shanghai': 6000,
            'Turkey_Iskenderun': 300,
            'Russia_Novorossiysk': 800
        }
        
    def calculate_breakbulk_cost(self, origin, date=None, market_quote=None):
        """Calculate breakbulk shipping cost with comparison to market quotes"""
        # Get BSI rate
        if date is not None:
            date = pd.to_datetime(date).date()
            idx = self.bsi.index.get_indexer([pd.Timestamp(date)], method='ffill')[0]
            bsi_rate = self.bsi.iloc[idx, 0]
        else:
            bsi_rate = self.bsi.iloc[-1, 0]
        
        # Base calculation
        base_cost = (bsi_rate / self.vessel_capacity) * (self.route_distances[origin] / 1000)
        
        # Apply breakbulk premium
        country = origin.split('_')[0]
        breakbulk_cost = base_cost * self.breakbulk_factors[country]
        
        # Add fees and surcharges
        total_cost = breakbulk_cost + self.port_fees[country] + self.port_fees['Israel']
        
        # China route premium
        if 'China' in origin:
            total_cost *= 1.25  # Additional Red Sea risk adjustment
        
        # Compare with market quote if provided
        comparison = {}
        if market_quote:
            difference = total_cost - market_quote
            percentage_diff = (difference / market_quote) * 100
            comparison = {
                'Calculated_Cost': round(total_cost, 2),
                'Market_Quote': market_quote,
                'Difference': round(difference, 2),
                'Percentage_Diff': round(percentage_diff, 2)
            }
        
        return round(total_cost, 2), comparison
    
    def generate_comparison_report(self, market_quotes=None):
        """Generate report with market comparisons"""
        if market_quotes is None:
            market_quotes = {
                'China_Shanghai': None,
                'Turkey_Iskenderun': None,
                'Russia_Novorossiysk': None
            }
        
        report_data = []
        for route in market_quotes.keys():
            cost, comparison = self.calculate_breakbulk_cost(
                route, 
                market_quote=market_quotes[route]
            )
            
            report_data.append({
                'Route': f"{route.split('_')[0]}-Israel",
                'Calculated_Breakbulk': cost,
                'Market_Quote': market_quotes[route] if market_quotes[route] else 'N/A',
                'Difference': comparison.get('Difference', 'N/A'),
                'Percentage_Diff': f"{comparison.get('Percentage_Diff', 'N/A')}%" if comparison else 'N/A',
                '30d_Avg': self.calculate_breakbulk_cost(
                    route, 
                    pd.Timestamp.now() - pd.Timedelta(days=30)
                )[0]
            })
        
        return pd.DataFrame(report_data)

# 4. Initialize with sample market quotes (replace with actual quotes)
market_quotes = {
    'China_Shanghai': 145.50,  # Example market quote for China-Israel
    'Turkey_Iskenderun': 48.75,
    'Russia_Novorossiysk': 62.30
}

calculator = BreakbulkFreightCalculator(bsi_df)
comparison_report = calculator.generate_comparison_report(market_quotes)

# 5. Enhanced Visualization
plt.figure(figsize=(14, 10))
sns.set_style("whitegrid")

# Plot 1: BSI Trend
ax1 = plt.subplot(3, 1, 1)
bsi_df.plot(ax=ax1, color='navy', linewidth=2)
plt.title('Baltic Supramax Index (BSI) Trend', pad=20)
plt.ylabel('USD/Day')

# Plot 2: Breakbulk Cost vs Market Quotes
ax2 = plt.subplot(3, 1, 2)
routes = ['China-Israel', 'Turkey-Israel', 'Russia-Israel']
calculated = [comparison_report[comparison_report['Route'] == r]['Calculated_Breakbulk'].values[0] for r in routes]
quotes = [market_quotes['China_Shanghai'], market_quotes['Turkey_Iskenderun'], market_quotes['Russia_Novorossiysk']]

x = range(len(routes))
width = 0.35
ax2.bar(x, calculated, width, label='Calculated', color='royalblue')
ax2.bar([i + width for i in x], quotes, width, label='Market Quotes', color='orange')

plt.xticks([i + width/2 for i in x], routes)
plt.title('Breakbulk Cost vs Market Quotes', pad=20)
plt.ylabel('USD/ton')
plt.legend()

# Plot 3: Percentage Difference
ax3 = plt.subplot(3, 1, 3)
diffs = [float(comparison_report[comparison_report['Route'] == r]['Percentage_Diff'].values[0].replace('%', '')) for r in routes]
colors = ['red' if diff > 0 else 'green' for diff in diffs]
ax3.bar(routes, diffs, color=colors)
plt.title('Calculated vs Market Quote Difference (%)', pad=20)
plt.ylabel('Percentage Difference')
plt.axhline(0, color='black', linewidth=0.8)

plt.tight_layout()

# Save report and visualization
os.makedirs(r"C:\Users\<USER>\Code\shipment\reports", exist_ok=True)
comparison_report.to_csv(r"C:\Users\<USER>\Code\shipment\reports\breakbulk_comparison.csv", index=False)
plt.savefig(r"C:\Users\<USER>\Code\shipment\reports\breakbulk_analysis.png")

print("\n=== Breakbulk Cost Comparison Report ===")
print(comparison_report)
print("\nReports saved to shipment/reports directory")