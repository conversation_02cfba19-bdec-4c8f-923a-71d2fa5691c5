from playwright.sync_api import sync_playwright
import json

def capture_api_calls():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)  # Set headless=True for background operation
        context = browser.new_context()
        page = context.new_page()
        
        # Enable request interception
        api_calls = []
        
        def log_request(request):
            if any(keyword in request.url for keyword in ['api', 'data', 'json']):
                api_calls.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers),
                    'post_data': request.post_data
                })
        
        page.on('request', log_request)
        
        # Navigate to target page
        page.goto('https://www.kline.co.jp/en/ir/finance/shipping.html', wait_until='networkidle')
        
        # Wait for potential API calls to complete
        page.wait_for_timeout(5000)
        
        # Save captured API calls
        with open('captured_api_calls.json', 'w') as f:
            json.dump(api_calls, f, indent=2)
        
        browser.close()
        
        print(f"Captured {len(api_calls)} potential API calls")
        print("Saved to captured_api_calls.json")

if __name__ == "__main__":
    capture_api_calls()
