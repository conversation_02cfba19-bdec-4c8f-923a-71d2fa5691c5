import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import seaborn as sns
import os
import numpy as np

# 1. Load BSI Data from Local CSV
bsi_path = Path(r"C:\Users\<USER>\Code\shipment\data\shipping_indices\BSI_data.csv")
bsi_df = pd.read_csv(bsi_path)
bsi_df['date'] = pd.to_datetime(bsi_df['date'], dayfirst=True, format='mixed')
bsi_df.set_index('date', inplace=True)

# 2. Data Validation
print(f"Data Range: {bsi_df.index.min().date()} to {bsi_df.index.max().date()}")
print(f"Missing Values: {bsi_df.isnull().sum()}")

# 3. Voyage Parameters and Breakbulk Premiums
voyages = {
    'China_Tianjin': 24.7 + 23.7 + 4.0,
    'China_Rizhao': 24.3 + 23.3 + 4.0,
    'China_Shanghai': 24.0 + 23.0 + 4.5,
    'Russia_Novorossiysk': 2.8 + 2.7 + 3.5,
    'Turkey_Iskenderun': 1.2 + 1.2 + 3.0,
    'Turkey_Mersin': 1.0 + 1.0 + 3.0,
    'Turkey_NemrutBay': 1.8 + 1.7 + 3.5,
    'Portugal_Sines': 11.1 + 10.7 + 4.0,
    'Portugal_Leixoes': 11.5 + 11.0 + 4.0
}

breakbulk_premiums = {
    'China': 0.45,
    'Russia': 0.40,
    'Turkey': 0.35,
    'Portugal': 0.30,  # adjust as needed
}

# 4. Calculator Class
class BreakbulkFromBSI:
    def __init__(self, bsi_data, dwt=55000):
        self.bsi = bsi_data
        self.dwt = dwt

    def get_latest_bsi(self):
        return float(self.bsi.iloc[-1, 0])

    def compute_rate(self, route, date=None):
        days = voyages[route]
        if date:
            dt = pd.to_datetime(date)
            rate = float(self.bsi.loc[:dt].iloc[-1, 0])
        else:
            rate = self.get_latest_bsi()
        tce_per_ton = (rate * days) / self.dwt
        country = route.split('_')[0]
        premium = breakbulk_premiums.get(country, 0.0)
        market_rate = tce_per_ton * (1 + premium)
        return {
            'Route': route.replace('_', ' → '),
            'BSI_Rate_USD_per_day': rate,
            'Voyage_Days': days,
            'TCE_per_ton': round(tce_per_ton, 3),
            'Premium_%': premium * 100,
            'Market_Breakbulk_USD_per_ton': round(market_rate, 3)
        }

    def generate_dataframe(self, date=None):
        rows = [self.compute_rate(route, date) for route in voyages]
        return pd.DataFrame(rows)

# 5. Run Calculations
calculator = BreakbulkFromBSI(bsi_df)
df_today = calculator.generate_dataframe()  # latest BSI
df_30d = calculator.generate_dataframe(date=pd.Timestamp.now() - pd.Timedelta(days=30))

# Combine comparison
comp = df_today.copy()
comp = comp.merge(
    df_30d[['Route', 'Market_Breakbulk_USD_per_ton']].rename(columns={'Market_Breakbulk_USD_per_ton': '30d_Avg_USD_per_ton'}),
    on='Route'
)

# 6. Visualization
sns.set_style("whitegrid")
plt.figure(figsize=(16, 12))

# Plot 1: Market Breakbulk per Ton
ax1 = plt.subplot(2, 1, 1)
sns.barplot(
    data=comp,
    x='Route',
    y='Market_Breakbulk_USD_per_ton',
    palette='Blues_d',
    ax=ax1
)
ax1.set_title('Market‑derived Breakbulk Cost per Ton (Based on Latest BSI)')
ax1.set_ylabel('USD/ton')
ax1.set_xticklabels(ax1.get_xticklabels(), rotation=45, ha='right')

# Plot 2: Comparison with 30‑day Ago
ax2 = plt.subplot(2, 1, 2)
x = np.arange(len(comp))
width = 0.35
ax2.bar(x - width/2, comp['Market_Breakbulk_USD_per_ton'], width, label='Latest', color='royalblue')
ax2.bar(x + width/2, comp['30d_Avg_USD_per_ton'], width, label='30 days ago', color='skyblue')
ax2.set_xticks(x)
ax2.set_xticklabels(comp['Route'], rotation=45, ha='right')
ax2.set_title('Latest vs 30‑Day Ago Breakbulk Rate (USD/ton)')
ax2.set_ylabel('USD/ton')
ax2.legend()

plt.tight_layout()

# Save results and plots
output_dir = Path(r"C:\Users\<USER>\Code\shipment\reports")
output_dir.mkdir(parents=True, exist_ok=True)
comp.to_csv(output_dir / "breakbulk_market_rates.csv", index=False)
plt.savefig(output_dir / "breakbulk_market_rates.png")

print("\n=== Breakbulk Market‑Derived Rates ===")
print(comp.to_string(index=False))
print(f"\nReports saved to: {output_dir}")
