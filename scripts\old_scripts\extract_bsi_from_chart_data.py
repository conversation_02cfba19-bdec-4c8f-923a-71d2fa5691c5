import json
import csv
from datetime import datetime

def extract_bsi_data():
    """
    Extract BSI (Baltic Supramax Index) data from the Highcharts runtime data
    """
    
    # Load the extracted chart data
    with open('highcharts_runtime_data.json', 'r') as f:
        charts_data = json.load(f)
    
    print(f"Found {len(charts_data)} charts")
    
    # Find the chart containing BSI data
    bsi_data = None
    for chart in charts_data:
        print(f"Chart {chart['index']}: {chart['title']}")
        
        # Look for the chart with BSI data
        if 'BSI' in chart['title'] or any('BSI' in series['name'] for series in chart['series']):
            print(f"  Found BSI data in chart {chart['index']}")
            
            # Find the BSI series
            for series in chart['series']:
                if series['name'] == 'BSI':
                    bsi_data = series['data']
                    print(f"  BSI series has {len(bsi_data)} data points")
                    break
            break
    
    if not bsi_data:
        print("BSI data not found!")
        return []
    
    # Process the BSI data
    processed_data = []
    for point in bsi_data:
        if point['y'] is not None:  # Skip null values
            processed_data.append({
                'date': point['category'],
                'bsi_index': point['y'],
                'x_index': point['x']
            })
    
    print(f"Processed {len(processed_data)} BSI data points")
    
    # Save to CSV
    csv_filename = 'bsi_index_extracted.csv'
    with open(csv_filename, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=['date', 'bsi_index', 'x_index'])
        writer.writeheader()
        writer.writerows(processed_data)
    
    print(f"BSI data saved to {csv_filename}")
    
    # Show sample data
    print("\nSample BSI data:")
    print("Date\t\tBSI Index")
    print("-" * 30)
    for i in range(min(10, len(processed_data))):
        point = processed_data[i]
        print(f"{point['date']}\t{point['bsi_index']}")
    
    if len(processed_data) > 10:
        print("...")
        print(f"Last entry: {processed_data[-1]['date']}\t{processed_data[-1]['bsi_index']}")
    
    # Show date range
    if processed_data:
        first_date = processed_data[0]['date']
        last_date = processed_data[-1]['date']
        print(f"\nDate range: {first_date} to {last_date}")
        print(f"Total data points: {len(processed_data)}")
        
        # Show some statistics
        values = [point['bsi_index'] for point in processed_data]
        min_val = min(values)
        max_val = max(values)
        avg_val = sum(values) / len(values)
        
        print(f"\nBSI Index Statistics:")
        print(f"Minimum: {min_val}")
        print(f"Maximum: {max_val}")
        print(f"Average: {avg_val:.2f}")
    
    return processed_data

def extract_all_shipping_indices():
    """
    Extract all shipping indices from the chart data
    """
    with open('highcharts_runtime_data.json', 'r') as f:
        charts_data = json.load(f)
    
    shipping_indices = {}
    
    for chart in charts_data:
        title = chart['title']
        
        # Look for shipping-related charts
        if any(keyword in title.upper() for keyword in ['BDI', 'BCI', 'BPI', 'BSI', 'BHSI', 'BULK', 'FREIGHT']):
            print(f"\nFound shipping chart: {title}")
            
            for series in chart['series']:
                series_name = series['name']
                data_points = []
                
                for point in series['data']:
                    if point['y'] is not None:
                        data_points.append({
                            'date': point['category'],
                            'value': point['y']
                        })
                
                if data_points:
                    shipping_indices[series_name] = data_points
                    print(f"  {series_name}: {len(data_points)} data points")
    
    # Save each index to separate CSV files
    for index_name, data in shipping_indices.items():
        filename = f"{index_name.lower()}_index_data.csv"
        with open(filename, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=['date', 'value'])
            writer.writeheader()
            writer.writerows(data)
        print(f"Saved {index_name} data to {filename}")
    
    return shipping_indices

if __name__ == "__main__":
    print("BSI Data Extractor from Chart Data")
    print("=" * 50)
    
    # Extract BSI data specifically
    bsi_data = extract_bsi_data()
    
    print("\n" + "=" * 50)
    print("Extracting all shipping indices...")
    
    # Extract all shipping indices
    all_indices = extract_all_shipping_indices()
    
    print(f"\nExtraction complete!")
    print(f"Available indices: {list(all_indices.keys())}")
    
    if 'BSI' in all_indices:
        print(f"\nBSI data successfully extracted with {len(all_indices['BSI'])} data points")
        print("File: bsi_index_data.csv")
    else:
        print("\nBSI data not found in shipping indices")
