import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

# 1. Data Loading and Integration
class ShippingDataIntegrator:
    def __init__(self):
        self.data_path = Path(r"C:\Users\<USER>\Code\shipment\data\shipping_indices")
        self.drewry_breakbulk = {
            'MPP_Index': 125.4,  # Example Drewry Multi-Purpose Index value
            'HeavyLift_Index': 118.2,
            'Conversion_Factors': {
                'China': 0.85,
                'Mediterranean': 0.72
            }
        }
    
    def load_all_indices(self):
        """Load all Baltic indices and merge with Drewry data"""
        indices = {}
        for file in self.data_path.glob('*.csv'):
            name = file.stem.split('_')[0]
            df = pd.read_csv(file, parse_dates=['date'], dayfirst=True)
            df.set_index('date', inplace=True)
            df = df[~df.index.duplicated(keep='last')]  # Ensure unique index
            indices[name] = df
        
        # Create composite dataset
        combined = pd.concat(indices.values(), axis=1, keys=indices.keys())
        return combined

# 2. Breakbulk Cost Calculator
class BreakbulkCostEngine:
    def __init__(self, indices_data, drewry_breakbulk):
        self.data = indices_data
        self.drewry_breakbulk = drewry_breakbulk
        self.vessel_params = {
            'MPP': {
                'dwt': 25000,
                'speed': 12.5,
                'daily_cost': 8500  # Base from Drewry
            },
            'HeavyLift': {
                'dwt': 18000,
                'speed': 11.5,
                'daily_cost': 12000
            }
        }
        self.route_distances = {
            'China-Israel': 6000,
            'Turkey-Israel': 300,
            'Russia-Israel': 800,
            'Portugal-Israel': 2500
        }

    def calculate_costs(self, route, cargo_type='MPP'):
        """Calculate breakbulk costs using hybrid model"""
        # Get relevant indices
        bsi = self.data['BSI'].iloc[-1,0]
        drewry_index = self.drewry_breakbulk[f'{cargo_type}_Index']
        
        # Calculate voyage components
        distance = self.route_distances[route]
        vessel = self.vessel_params[cargo_type]
        
        # Hybrid rate calculation (Baltic + Drewry adjustment)
        daily_rate = (bsi * 0.6) + (drewry_index * 40)  # Weighted formula
        
        # Voyage time calculation
        sea_days = distance / (vessel['speed'] * 24)
        total_days = (sea_days * 2) + 4  # Round trip + 4 port days
        
        # Cost breakdown
        voyage_cost = daily_rate * total_days
        handling_cost = 25 if cargo_type == 'MPP' else 40  # USD/ton
        port_fees = 18 if 'China' in route else 15
        
        # Total cost per ton
        total = (voyage_cost / vessel['dwt']) + handling_cost + port_fees
        
        # Apply regional factors
        region = route.split('-')[0]
        total *= self.drewry_breakbulk['Conversion_Factors'].get(region, 1.0)
        
        return round(total, 2)

# 3. Implementation
if __name__ == "__main__":
    # Initialize and load data
    integrator = ShippingDataIntegrator()
    combined_data = integrator.load_all_indices()
    
    # Setup calculator
    calculator = BreakbulkCostEngine(combined_data, integrator.drewry_breakbulk)
    
    # Generate current rates
    routes = ['China-Israel', 'Turkey-Israel', 'Russia-Israel', 'Portugal-Israel']
    current_rates = {route: calculator.calculate_costs(route) for route in routes}
    
    # Create output report
    report = pd.DataFrame({
        'Route': routes,
        'Breakbulk_Rate_USD_per_ton': current_rates.values(),
        'Vessel_Type': 'MPP',
        'Calculation_Date': pd.Timestamp.now().date()
    })
    
    # Visualization
    plt.figure(figsize=(12, 6))
    sns.barplot(data=report, x='Route', y='Breakbulk_Rate_USD_per_ton', palette='Blues_d')
    plt.title('Current Breakbulk Shipping Rates to Israel')
    plt.ylabel('USD per Metric Ton')
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # Save outputs
    output_dir = Path(r"C:\Users\<USER>\Code\shipment\reports")
    output_dir.mkdir(exist_ok=True)
    
    report.to_csv(output_dir / "breakbulk_cost_report.csv", index=False)
    plt.savefig(output_dir / "breakbulk_rates.png")
    
    print("Breakbulk Cost Report Generated:")
    print(report)