import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import seaborn as sns
import os

# 1. Load BSI Data from Local CSV
bsi_path = Path(r"C:\Users\<USER>\Code\shipment\data\shipping_indices\BSI_data.csv")
bsi_df = pd.read_csv(bsi_path)
bsi_df['date'] = pd.to_datetime(bsi_df['date'], format='mixed', dayfirst=True)
bsi_df.set_index('date', inplace=True)

# 2. Data Validation
print(f"Data Range: {bsi_df.index.min().date()} to {bsi_df.index.max().date()}")
print(f"Missing Values: {bsi_df.isnull().sum()}")

# 3. Freight Cost Calculator Class
class FreightCostEngine:
    def __init__(self, bsi_data):
        self.bsi = bsi_data
        self.vessel_capacity = 50000  # Standard Supramax DWT
        self.port_fees = {
            'China': 18,
            'Turkey': 12,
            'Russia': 15,
            'Israel': 10
        }
        
    def calculate_freight(self, origin, date=None):
        """Calculate USD/ton freight cost for a route"""
        distance_map = {
            'China_Shanghai': 6000,
            'Turkey_Iskenderun': 300,
            'Russia_Novorossiysk': 800
        }
        # If date is provided, use only the date part and get the closest available index
        if date is not None:
            date = pd.to_datetime(date).date()
            # Find the closest previous date in the index
            idx = self.bsi.index.get_indexer([pd.Timestamp(date)], method='ffill')[0]
            bsi_rate = self.bsi.iloc[idx, 0]
        else:
            bsi_rate = self.bsi.iloc[-1, 0]
        # Base freight calculation
        base_cost = (bsi_rate / self.vessel_capacity) * (distance_map[origin] / 1000)
        # Add surcharges
        total_cost = base_cost + self.port_fees[origin.split('_')[0]] + self.port_fees['Israel']
        # China route premium
        if 'China' in origin:
            total_cost *= 1.15  # Red Sea risk adjustment
        return round(total_cost, 2)
    
    def generate_report(self):
        """Create weekly freight cost analysis"""
        report = pd.DataFrame({
            'Route': ['China-Israel', 'Turkey-Israel', 'Russia-Israel'],
            'Current Rate': [
                self.calculate_freight('China_Shanghai'),
                self.calculate_freight('Turkey_Iskenderun'),
                self.calculate_freight('Russia_Novorossiysk')
            ],
            '30d Avg': [
                self.calculate_freight('China_Shanghai', pd.Timestamp.now() - pd.Timedelta(days=30)),
                self.calculate_freight('Turkey_Iskenderun', pd.Timestamp.now() - pd.Timedelta(days=30)),
                self.calculate_freight('Russia_Novorossiysk', pd.Timestamp.now() - pd.Timedelta(days=30))
            ]
        })
        return report

# 4. Initialize and Run Analysis
engine = FreightCostEngine(bsi_df)

# Current Market Snapshot
current_report = engine.generate_report()
print("\nCurrent Freight Cost Report (USD/ton):")
print(current_report)

# 5. Visualization
plt.figure(figsize=(12, 6))
sns.set_style("whitegrid")

# Plot BSI Trend
ax1 = plt.subplot(2, 1, 1)
bsi_df.plot(ax=ax1, color='royalblue', linewidth=2)
plt.title('Baltic Supramax Index (BSI) Trend')
plt.ylabel('USD/Day')

# Plot Calculated Freight Costs
ax2 = plt.subplot(2, 1, 2)
for route in ['China_Shanghai', 'Turkey_Iskenderun', 'Russia_Novorossiysk']:
    costs = [engine.calculate_freight(route, date) for date in bsi_df.index]
    pd.Series(costs, index=bsi_df.index).plot(label=route.split('_')[0], ax=ax2)

plt.title('Calculated Freight Costs to Israel')
plt.ylabel('USD/ton')
plt.legend()

plt.tight_layout()

# Ensure the reports directory exists before saving the figure
os.makedirs(r"C:\Users\<USER>\Code\shipment\reports", exist_ok=True)

plt.savefig(r"C:\Users\<USER>\Code\shipment\reports\freight_analysis.png")
print("\nReport saved to shipment/reports directory")